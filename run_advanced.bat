@echo off
setlocal enabledelayedexpansion

:: ========================================
::   Gemini Image Generator - Advanced
:: ========================================

:: Parse command line arguments
set "PROMPT="
set "OUTPUT_FILE=generated_image.png"
set "API_KEY="
set "HELP=0"
set "LIST_MODELS=0"

:parse_args
if "%~1"=="" goto :end_parse_args
if /i "%~1"=="--help" (
    set "HELP=1"
    goto :show_help
)
if /i "%~1"=="-h" (
    set "HELP=1"
    goto :show_help
)
if /i "%~1"=="--output" (
    set "OUTPUT_FILE=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="-o" (
    set "OUTPUT_FILE=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--api-key" (
    set "API_KEY=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="-k" (
    set "API_KEY=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--list-models" (
    set "LIST_MODELS=1"
    shift
    goto :parse_args
)
if not defined PROMPT (
    set "PROMPT=%~1"
    shift
    goto :parse_args
) else (
    set "PROMPT=!PROMPT! %~1"
    shift
    goto :parse_args
)
:end_parse_args

:: Show help if requested
:show_help
if "%HELP%"=="1" (
    echo.
    echo Gemini Image Generator - Command Line Interface
    echo ===============================================
    echo.
    echo Usage: run_advanced.bat [options] "your prompt text"
    echo.
    echo Options:
    echo   -o, --output FILE     Specify output filename (default: generated_image.png)
    echo   -k, --api-key KEY     Specify Gemini API key (overrides environment variable)
    echo   -h, --help            Show this help message
    echo   --list-models         List available models and exit
    echo.
    echo Examples:
    echo   run_advanced.bat "A futuristic city at sunset"
    echo   run_advanced.bat -o landscape.png "A beautiful mountain landscape"
    echo   run_advanced.bat --list-models
    echo.
    exit /b 0
)

:: Set default prompt if none provided
if not defined PROMPT (
    set "PROMPT=A futuristic city at sunset with flying cars"
)

echo ========================================
echo   Gemini Image Generator - Auto Setup
echo ========================================
echo.

:: Check if uv is installed
echo [1/6] Checking uv installation...
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: uv is not installed or not in PATH
    echo Please install uv first: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)
echo ✅ uv is available

:: Check Python version
echo [2/6] Checking Python version...
python -c "import sys; sys.exit(0 if sys.version_info >= (3, 9) else 1)" >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python 3.9 or higher is required
    echo Current Python version:
    python --version
    pause
    exit /b 1
)
echo ✅ Python 3.9+ is available

:: Check if .venv exists
echo [3/6] Checking virtual environment...
if exist ".venv" (
    echo ✅ Virtual environment already exists
) else (
    echo 🔧 Creating virtual environment...
    uv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

:: Check if packages are installed
echo [4/6] Checking dependencies...
call .venv\Scripts\activate.bat
python -c "import google.generativeai, PIL" >nul 2>&1
if errorlevel 1 (
    echo 🔧 Installing dependencies...
    uv sync
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
) else (
    echo ✅ Dependencies already installed
)

:: Check for API key
echo [5/6] Checking API key configuration...
if defined API_KEY (
    echo ✅ Using API key from command line
    set "GEMINI_API_KEY=%API_KEY%"
) else if not "%GEMINI_API_KEY%"=="" (
    echo ✅ Using API key from environment variable
) else (
    echo ⚠️  Warning: No API key provided
    echo You need to set GEMINI_API_KEY environment variable or use --api-key option
    echo.
    set /p API_KEY="Enter your Gemini API key (or press Enter to exit): "
    if "!API_KEY!"=="" (
        echo ❌ No API key provided. Exiting.
        pause
        exit /b 1
    )
    set "GEMINI_API_KEY=!API_KEY!"
    echo ✅ API key set
)

:: Run the application
echo [6/6] Running Gemini Image Generator...
echo.

if "%LIST_MODELS%"=="1" (
    echo Listing available models...
    uv run gemini-img --list-models
) else (
    echo Prompt: %PROMPT%
    echo Output: %OUTPUT_FILE%
    echo.
    uv run gemini-img "%PROMPT%" -o "%OUTPUT_FILE%"
)
set EXIT_CODE=%errorlevel%

if %EXIT_CODE% EQU 0 (
    echo.
    echo ✅ Image generation completed successfully!
    echo Image saved as: %OUTPUT_FILE%
) else (
    echo.
    echo ❌ Image generation failed with exit code %EXIT_CODE%
)

echo.
echo ========================================
echo   Execution completed!
echo ========================================
pause
