"""Core image generation functionality."""

import os
import io
import requests
import base64
from typing import Optional

import google.generativeai as genai
from PIL import Image


def configure_api(api_key: Optional[str] = None, use_openrouter: bool = False) -> None:
    """Configure the API with the provided key or environment variable."""
    if use_openrouter:
        # For OpenRouter, we don't need to configure anything here
        # The API key will be used directly in requests
        key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not key:
            raise ValueError("OpenRouter API key must be provided or set in OPENROUTER_API_KEY environment variable")
    else:
        # Original Gemini API configuration
        key = api_key or os.getenv("GEMINI_API_KEY")
        if not key:
            raise ValueError("Gemini API key must be provided or set in GEMINI_API_KEY environment variable")
        genai.configure(api_key=key)


def generate_image_openrouter(
    prompt: str,
    output_filename: str = "generated_image.png",
    model_name: str = "black-forest-labs/flux-schnell-free"
) -> bool:
    """
    Generate an image using OpenRouter API and save it.

    Args:
        prompt: Text description for image generation
        output_filename: Name of the file to save the image as
        model_name: The OpenRouter model to use for image generation

    Returns:
        True if successful, False otherwise
    """
    try:
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OPENROUTER_API_KEY environment variable not set")
            return False

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_name,
            "prompt": prompt,
            "max_tokens": 1000,
            "response_format": {"type": "url"}  # Request image URL
        }

        print(f"🔄 Generating image using OpenRouter model: {model_name}")
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()

            # Handle different response formats
            if "choices" in result and result["choices"]:
                content = result["choices"][0].get("message", {}).get("content", "")

                # Try to extract image URL or base64 data
                if content.startswith("http"):
                    # Download image from URL
                    img_response = requests.get(content)
                    if img_response.status_code == 200:
                        with open(output_filename, 'wb') as f:
                            f.write(img_response.content)
                        print(f"✅ Image successfully generated and saved as '{output_filename}'")
                        return True
                elif content.startswith("data:image"):
                    # Handle base64 encoded image
                    image_data = content.split(",")[1]
                    image_bytes = base64.b64decode(image_data)
                    with open(output_filename, 'wb') as f:
                        f.write(image_bytes)
                    print(f"✅ Image successfully generated and saved as '{output_filename}'")
                    return True
                else:
                    print(f"❌ Unexpected response format: {content}")
                    return False
            else:
                print(f"❌ No image data in response: {result}")
                return False
        else:
            print(f"❌ OpenRouter API error: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ An error occurred with OpenRouter: {e}")
        return False


def generate_and_save_image(
    prompt: str,
    output_filename: str = "generated_image.png",
    model_name: str = "models/gemini-2.0-flash-preview-image-generation",
    use_openrouter: bool = False
) -> bool:
    """
    Generate an image using the specified model and save it.

    Args:
        prompt: Text description for image generation
        output_filename: Name of the file to save the image as
        model_name: The name of the image generation model to use
        use_openrouter: Whether to use OpenRouter instead of Gemini API

    Returns:
        True if successful, False otherwise
    """
    if use_openrouter:
        # Use OpenRouter for image generation
        return generate_image_openrouter(prompt, output_filename, model_name)

    try:
        # Use the specified model for image generation via Gemini API
        model = genai.GenerativeModel(model_name)
        response = model.generate_content(prompt)

        if hasattr(response, 'images') and response.images:
            # Save the first generated image
            image_bytes = response.images[0].pixels
            image = Image.open(io.BytesIO(image_bytes))
            image.save(output_filename)
            print(f"✅ Image successfully generated and saved as '{output_filename}'")
            return True
        else:
            print("❌ No image was returned by the API.")
            print("This is likely because:")
            print("  1. The model doesn't support image generation")
            print("  2. Your API key doesn't have access to image generation features")
            print("  3. You need to enable billing in your Google Cloud project")
            print("\nConsider using OpenRouter as an alternative:")
            print("  uv run gemini-img --openrouter \"Your prompt here\"")
            return False

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        if "404" in str(e) and "not found" in str(e):
            print("\nThis error indicates that the specified model is not available for your API key.")
            print("To use image generation with Gemini API, you likely need to:")
            print("  1. Enable billing in your Google Cloud project")
            print("  2. Ensure you're using a supported region")
            print("  3. Visit Google AI Studio (https://aistudio.google.com) to check your access")
            print("\nAlternatively, try using OpenRouter:")
            print("  uv run gemini-img --openrouter \"Your prompt here\"")
        return False
