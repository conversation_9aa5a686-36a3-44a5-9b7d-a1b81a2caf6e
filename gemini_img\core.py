"""Core image generation functionality."""

import os
import io
from typing import Optional

import google.generativeai as genai
from PIL import Image


def configure_api(api_key: Optional[str] = None) -> None:
    """Configure the Gemini API with the provided key or environment variable."""
    key = api_key or os.getenv("GEMINI_API_KEY")
    if not key:
        raise ValueError("API key must be provided or set in GEMINI_API_KEY environment variable")
    genai.configure(api_key=key)


def generate_and_save_image(
    prompt: str,
    output_filename: str = "generated_image.png",
    model_name: str = "models/gemini-2.0-flash-preview-image-generation"
) -> bool:
    """
    Generate an image using the specified Imagen model and save it.

    Args:
        prompt: Text description for image generation
        output_filename: Name of the file to save the image as
        model_name: The name of the image generation model to use (default: gemini-2.0-flash-preview-image-generation)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Use the specified model for image generation
        model = genai.GenerativeModel(model_name)
        response = model.generate_content(prompt)

        if response.images:
            # Save the first generated image
            image_bytes = response.images[0].pixels
            image = Image.open(io.BytesIO(image_bytes))
            image.save(output_filename)
            print(f"✅ Image successfully generated and saved as '{output_filename}'")
            return True
        else:
            print("❌ No image was returned by the API.")
            return False

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        return False
