"""Core image generation functionality."""

import os
import io
from typing import Optional

import google.generativeai as genai
from PIL import Image


def configure_api(api_key: Optional[str] = None) -> None:
    """Configure the Gemini API with the provided key or environment variable."""
    key = api_key or os.getenv("GEMINI_API_KEY")
    if not key:
        raise ValueError("API key must be provided or set in GEMINI_API_KEY environment variable")
    genai.configure(api_key=key)


def generate_and_save_image(
    prompt: str,
    output_filename: str = "generated_image.png",
    model_name: str = "models/gemini-2.0-flash-preview-image-generation"
) -> bool:
    """
    Generate an image using the specified model and save it.

    Args:
        prompt: Text description for image generation
        output_filename: Name of the file to save the image as
        model_name: The name of the image generation model to use

    Returns:
        True if successful, False otherwise
    """
    try:
        # Use the specified model for image generation
        model = genai.GenerativeModel(model_name)
        response = model.generate_content(prompt)

        if hasattr(response, 'images') and response.images:
            # Save the first generated image
            image_bytes = response.images[0].pixels
            image = Image.open(io.BytesIO(image_bytes))
            image.save(output_filename)
            print(f"✅ Image successfully generated and saved as '{output_filename}'")
            return True
        else:
            print("❌ No image was returned by the API.")
            print("This is likely because:")
            print("  1. The model doesn't support image generation")
            print("  2. Your API key doesn't have access to image generation features")
            print("  3. You need to enable billing in your Google Cloud project")
            print("\nPlease visit Google AI Studio (https://aistudio.google.com) to check your access")
            return False

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        if "404" in str(e) and "not found" in str(e):
            print("\nThis error indicates that the specified model is not available for your API key.")
            print("To use image generation with Gemini API, you likely need to:")
            print("  1. Enable billing in your Google Cloud project")
            print("  2. Ensure you're using a supported region")
            print("  3. Visit Google AI Studio (https://aistudio.google.com) to check your access")
        return False
