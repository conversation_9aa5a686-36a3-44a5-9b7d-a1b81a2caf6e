# Gemini Image Generation Setup Guide

## Current Status
Your API key is working for text generation models but doesn't have access to image generation models.

## What You Need to Do

### 1. Enable Billing (Required for Image Generation)
Image generation models (Imagen) require a paid Google Cloud project:

1. Visit [Google AI Studio](https://aistudio.google.com)
2. Click "Get API key" in the left sidebar
3. Follow the prompts to enable billing for your project
4. Even with billing enabled, you may get free tier usage initially

### 2. Check Regional Availability
- Norway is a supported region for the Gemini API
- However, specific image generation features may have additional restrictions
- Some features require the `personGeneration: "allow_adult"` setting (default) rather than `"allow_all"` in EU regions

### 3. Alternative Approaches

#### Option A: Use Google AI Studio Directly
- Visit [Google AI Studio](https://aistudio.google.com)
- Try creating an image generation project directly in the web interface
- This will help you verify if image generation is available for your account

#### Option B: Use Vertex AI (Enterprise Solution)
If you need guaranteed access to image generation:
- Set up a Google Cloud project with Vertex AI
- Use the Vertex AI Gemini API instead of the Developer API
- This requires more setup but provides more control over regions and features

### 4. Test Your Setup
After enabling billing, wait a few minutes and then run:
```bash
uv run gemini-img --list-models
```

Look for models like:
- `imagen-3.0-generate-002`
- `imagen-4.0-generate-preview-06-06`
- `models/gemini-2.0-flash-preview-image-generation`

## Current Available Models
Your API key currently has access to these text generation models:
- Gemini 1.5 Pro/Flash (various versions)
- Gemini 2.0 Flash (various versions)
- Gemini 2.5 Pro/Flash (various versions)
- Gemma models

But none of these support image generation output.

## Next Steps
1. Enable billing in your Google Cloud project
2. Wait a few minutes for changes to propagate
3. Test again with: `uv run gemini-img "A simple red apple"`
4. If still not working, contact Google Cloud support for your specific region
