"""Utility functions for Gemini image generation."""

import google.generativeai as genai
from typing import Optional


def list_available_models():
    """List all available models that support content generation."""
    print("Listing models that support 'generateContent':")
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            print(f"- {m.name}")

    print("\nListing models that support image generation (based on metadata):")
    found_image_models = False
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            # Check if model supports image output
            if hasattr(m, 'supported_output_mime_types') and m.supported_output_mime_types:
                if any('image' in mime_type for mime_type in m.supported_output_mime_types):
                    print(f"- {m.name} (Output types: {m.supported_output_mime_types})")
                    found_image_models = True
    if not found_image_models:
        print("No models supporting image generation found in metadata.")

    print("\nTesting known image generation models:")
    known_image_models = [
        "imagen-3.0-generate-002",
        "imagen-4.0-generate-preview-06-06",
        "imagen-4.0-ultra-generate-preview-06-06",
        "gemini-2.0-flash-preview-image-generation"
    ]

    working_models = []
    for model_name in known_image_models:
        if test_model_availability(model_name):
            working_models.append(model_name)

    if working_models:
        print("\nWorking image generation models for your API key:")
        for model in working_models:
            print(f"- {model}")
    else:
        print("\nNo working image generation models found for your API key.")
        print("You may need to enable image generation in your Google Cloud project")
        print("or check regional availability for these features.")


def get_first_image_model() -> Optional[str]:
    """
    Attempts to find and return the name of the first available model
    that supports image generation.
    """
    # First, try to find models through API metadata
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            if hasattr(m, 'supported_output_mime_types') and m.supported_output_mime_types:
                if any('image' in mime_type for mime_type in m.supported_output_mime_types):
                    # Prefer models that explicitly mention "imagen" if available
                    if "imagen" in m.name.lower():
                        return m.name
                    # Otherwise, return the first one found
                    return m.name

    # If no models found through metadata, test known image generation models
    known_image_models = [
        # Most likely to work based on the list_models output
        "models/gemini-2.0-flash-preview-image-generation",
        # Vision models that might support image generation
        "models/gemini-1.0-pro-vision-latest",
        "models/gemini-pro-vision",
        # Imagen models (require paid tier/billing)
        "imagen-3.0-generate-002",
        "imagen-4.0-generate-preview-06-06",
        "imagen-4.0-ultra-generate-preview-06-06"
    ]

    for model_name in known_image_models:
        try:
            model = genai.GenerativeModel(model_name)
            # Test with a simple image generation prompt
            response = model.generate_content("A simple red circle")
            if response.images:
                return model_name
        except Exception:
            continue

    return None


def test_model_availability(model_name: str) -> bool:
    """Test if a specific model is available and working for image generation."""
    try:
        # First check if we can instantiate the model
        model = genai.GenerativeModel(model_name)

        # For image models, test with a simple image generation prompt
        if "imagen" in model_name.lower() or "image" in model_name.lower():
            try:
                response = model.generate_content("A simple red circle")
                if response.images:
                    print(f"✅ Model '{model_name}' is available and working for image generation")
                    return True
                else:
                    print(f"⚠️ Model '{model_name}' responds but doesn't generate images")
                    return False
            except Exception as e:
                print(f"⚠️ Model '{model_name}' can be instantiated but fails on image generation: {e}")
                return False
        else:
            # For non-image models, just test basic generation
            try:
                response = model.generate_content("Test")
                print(f"✅ Model '{model_name}' is available and working")
                return True
            except Exception as e:
                print(f"⚠️ Model '{model_name}' can be instantiated but fails on generation: {e}")
                return False
    except Exception as e:
        print(f"❌ Model '{model_name}' is not available: {e}")
        return False


if __name__ == "__main__":
    from .core import configure_api
    
    try:
        configure_api()
        print("Available models:")
        list_available_models()
        
        print("\nTesting image generation models:")
        models_to_test = [
            "models/gemini-2.0-flash-preview-image-generation",
            "models/gemini-1.0-pro-vision-latest",
            "models/gemini-pro-vision",
            "imagen-3.0-generate-002",
            "imagen-4.0-generate-preview-06-06"
        ]

        for model in models_to_test:
            test_model_availability(model)

        print("\nAttempting to find the first available image model:")
        found_model = get_first_image_model()
        if found_model:
            print(f"Automatically detected model: {found_model}")
        else:
            print("No image generation models found.")

    except Exception as e:
        print(f"Error: {e}")
        print("Make sure to set your GEMINI_API_KEY environment variable")
