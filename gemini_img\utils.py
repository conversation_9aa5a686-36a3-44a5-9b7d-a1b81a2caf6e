"""Utility functions for Gemini image generation."""

import google.generativeai as genai


def list_available_models():
    """List all available models that support content generation."""
    print("Listing models that support 'generateContent':")
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            print(f"- {m.name}")
    
    print("\nListing models that support image generation:")
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            # Check if model supports image output
            if hasattr(m, 'supported_output_mime_types') and m.supported_output_mime_types:
                if any('image' in mime_type for mime_type in m.supported_output_mime_types):
                    print(f"- {m.name} (Output types: {m.supported_output_mime_types})")


def test_model_availability(model_name: str) -> bool:
    """Test if a specific model is available and working."""
    try:
        model = genai.GenerativeModel(model_name)
        print(f"✅ Model '{model_name}' is available")
        return True
    except Exception as e:
        print(f"❌ Model '{model_name}' is not available: {e}")
        return False


if __name__ == "__main__":
    from .core import configure_api
    
    try:
        configure_api()
        print("Available models:")
        list_available_models()
        
        print("\nTesting common image generation models:")
        models_to_test = [
            "imagen-3.0-generate-002",
            "imagen-4.0-generate-preview-06-06",
            "gemini-2.0-flash-preview-image-generation"
        ]
        
        for model in models_to_test:
            test_model_availability(model)
            
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure to set your GEMINI_API_KEY environment variable")
