@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   Gemini Image Generator - Auto Setup
echo ========================================
echo.

:: Check if uv is installed
echo [1/6] Checking uv installation...
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: uv is not installed or not in PATH
    echo Please install uv first: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)
echo ✅ uv is available

:: Check Python version
echo [2/6] Checking Python version...
python -c "import sys; sys.exit(0 if sys.version_info >= (3, 9) else 1)" >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python 3.9 or higher is required
    echo Current Python version:
    python --version
    pause
    exit /b 1
)
echo ✅ Python 3.9+ is available

:: Check if .venv exists
echo [3/6] Checking virtual environment...
if exist ".venv" (
    echo ✅ Virtual environment already exists
) else (
    echo 🔧 Creating virtual environment...
    uv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

:: Check if packages are installed
echo [4/6] Checking dependencies...
call .venv\Scripts\activate.bat
python -c "import google.generativeai, PIL" >nul 2>&1
if errorlevel 1 (
    echo 🔧 Installing dependencies...
    uv sync
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
) else (
    echo ✅ Dependencies already installed
)

:: Check for API key
echo [5/6] Checking API key configuration...
if "%GEMINI_API_KEY%"=="" (
    echo ⚠️  Warning: GEMINI_API_KEY environment variable not set
    echo You may need to set it or modify the script to use your API key
    echo.
) else (
    echo ✅ GEMINI_API_KEY is configured
)

:: Run the application
echo [6/6] Running Gemini Image Generator...
echo.

:: Check for special flags
if "%~1"=="--list-models" (
    echo Listing available models...
    uv run gemini-img --list-models
    goto :end
)

if "%~1"=="--openrouter" (
    if "%~2"=="" (
        echo Running with OpenRouter and default prompt...
        uv run gemini-img --openrouter "A futuristic city at sunset with flying cars"
    ) else (
        echo Running with OpenRouter and custom prompt: %~2
        uv run gemini-img --openrouter "%~2"
    )
    goto :end
)

:: Check if arguments were passed
if "%~1"=="" (
    echo Running with default prompt...
    uv run gemini-img "A futuristic city at sunset with flying cars"
) else (
    echo Running with custom prompt: %*
    uv run gemini-img %*
)

:end

echo.
echo ========================================
echo   Execution completed!
echo ========================================
pause
