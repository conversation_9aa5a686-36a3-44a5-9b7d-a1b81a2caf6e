# Technology Stack

## Core
- **Python 3.x** - Primary language
- **uv** - Modern dependency management (replacing pip)

## Dependencies
- **google-generativeai** - Google Gemini API client
- **Pillow** - Image processing library

## Architecture
- **Single-module design** - Minimal, focused functionality
- **Direct API integration** - Streamlined Gemini API usage
- **Environment-based configuration** - Secure API key management
