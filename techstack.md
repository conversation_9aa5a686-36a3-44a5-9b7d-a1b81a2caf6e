# Technology Stack

## Core
- **Python 3.9+** - Primary language
- **uv** - Modern dependency management (replacing pip)

## Dependencies
- **google-generativeai** - Google Gemini API client
- **Pillow** - Image processing library

## Architecture
- **Single-module design** - Minimal, focused functionality
- **Direct API integration** - Uses Imagen 3.0 for high-quality image generation
- **Environment-based configuration** - Secure API key management
- **Model discovery** - Built-in tools to list available models
