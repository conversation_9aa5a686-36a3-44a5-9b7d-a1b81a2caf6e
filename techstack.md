# Technology Stack

## Core
- **Python 3.9+** - Primary language
- **uv** - Modern dependency management (replacing pip)

## Dependencies
- **google-generativeai** - Google Gemini API client
- **Pillow** - Image processing library

## Architecture
- **Single-module design** - Minimal, focused functionality
- **Adaptive API integration** - Auto-detects available image generation models
- **Environment-based configuration** - Secure API key management
- **Model discovery** - Built-in tools to list and test available models
- **Fault tolerance** - Graceful fallback to alternative models when needed
