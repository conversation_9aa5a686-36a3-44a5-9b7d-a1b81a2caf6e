# Technology Stack

## Core
- **Python 3.9+** - Primary language
- **uv** - Modern dependency management (replacing pip)

## Dependencies
- **google-generativeai** - Google Gemini API client
- **Pillow** - Image processing library

## Architecture
- **Single-module design** - Minimal, focused functionality
- **Direct API integration** - Uses Gemini 2.0 Flash Preview Image Generation model
- **Environment-based configuration** - Secure API key management
