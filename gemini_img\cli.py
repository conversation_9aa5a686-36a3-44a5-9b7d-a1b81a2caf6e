"""Command-line interface for Gemini image generation."""

import argparse
import os
import sys

import google.generativeai as genai
from .core import configure_api, generate_and_save_image
from .utils import list_available_models, get_first_image_model


def main() -> int:
    """Run the CLI application."""
    parser = argparse.ArgumentParser(description="Generate images using Google Gemini API")
    parser.add_argument("prompt", nargs="?", help="Text prompt for image generation")
    parser.add_argument(
        "-o", "--output",
        default="generated_image.png",
        help="Output filename (default: generated_image.png)"
    )
    parser.add_argument(
        "-k", "--api-key",
        help="Gemini API key (can also use GEMINI_API_KEY environment variable)"
    )
    parser.add_argument(
        "-m", "--model",
        default="models/gemini-2.0-flash-preview-image-generation",
        help="Specify the image generation model to use (e.g., models/gemini-2.0-flash-preview-image-generation)"
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List available models and exit"
    )
    
    args = parser.parse_args()

    try:
        configure_api(args.api_key)

        if args.list_models:
            list_available_models()
            return 0

        if not args.prompt:
            parser.error("prompt is required unless --list-models is used")

        # If the default model fails, try to find an alternative
        model_to_use = args.model

        # Test if the specified model works for image generation
        try:
            model = genai.GenerativeModel(model_to_use)
            # Try a simple test to see if it can generate images
            test_response = model.generate_content("A simple red circle")
            if not hasattr(test_response, 'images') or not test_response.images:
                raise ValueError("Model does not support image generation")
        except Exception as e:
            print(f"Warning: Model '{model_to_use}' cannot be used for image generation: {e}", file=sys.stderr)
            print("Attempting to find another image generation model...", file=sys.stderr)

            alternative_model = get_first_image_model()
            if alternative_model:
                model_to_use = alternative_model
                print(f"Using detected model: '{model_to_use}'", file=sys.stderr)
            else:
                print("\nError: No suitable image generation model found.", file=sys.stderr)
                print("This could be due to:", file=sys.stderr)
                print("  1. Your API key doesn't have access to image generation models", file=sys.stderr)
                print("  2. Image generation is not available in your region", file=sys.stderr)
                print("  3. You need to enable billing or specific APIs in your Google Cloud project", file=sys.stderr)
                print("\nPlease:", file=sys.stderr)
                print("  - Run 'uv run gemini-img --list-models' to see available models", file=sys.stderr)
                print("  - Visit Google AI Studio (https://aistudio.google.com) to check your access", file=sys.stderr)
                print("  - See the README.md troubleshooting section for more details", file=sys.stderr)

                # Check if setup guide exists and suggest it
                if os.path.exists("setup_guide.md"):
                    print("\nA setup guide has been created for you:", file=sys.stderr)
                    print("  - Open 'setup_guide.md' for detailed instructions on enabling image generation", file=sys.stderr)

                return 1

        success = generate_and_save_image(args.prompt, args.output, model_to_use)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
