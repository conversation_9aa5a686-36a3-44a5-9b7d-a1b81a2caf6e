"""Command-line interface for Gemini image generation."""

import argparse
import os
import sys

from .core import configure_api, generate_and_save_image


def main() -> int:
    """Run the CLI application."""
    parser = argparse.ArgumentParser(description="Generate images using Google Gemini API")
    parser.add_argument("prompt", help="Text prompt for image generation")
    parser.add_argument(
        "-o", "--output", 
        default="generated_image.png", 
        help="Output filename (default: generated_image.png)"
    )
    parser.add_argument(
        "-k", "--api-key", 
        help="Gemini API key (can also use GEMINI_API_KEY environment variable)"
    )
    
    args = parser.parse_args()
    
    try:
        configure_api(args.api_key)
        success = generate_and_save_image(args.prompt, args.output)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
