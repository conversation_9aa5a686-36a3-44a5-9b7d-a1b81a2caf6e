"""Command-line interface for Gemini image generation."""

import argparse
import os
import sys

from .core import configure_api, generate_and_save_image
from .utils import list_available_models, get_first_image_model


def main() -> int:
    """Run the CLI application."""
    parser = argparse.ArgumentParser(description="Generate images using Google Gemini API")
    parser.add_argument("prompt", nargs="?", help="Text prompt for image generation")
    parser.add_argument(
        "-o", "--output",
        default="generated_image.png",
        help="Output filename (default: generated_image.png)"
    )
    parser.add_argument(
        "-k", "--api-key",
        help="Gemini API key (can also use GEMINI_API_KEY environment variable)"
    )
    parser.add_argument(
        "-m", "--model",
        default="imagen-3.0-generate-002",
        help="Specify the image generation model to use (e.g., imagen-3.0-generate-002)"
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List available models and exit"
    )
    
    args = parser.parse_args()

    try:
        configure_api(args.api_key)

        if args.list_models:
            list_available_models()
            return 0

        if not args.prompt:
            parser.error("prompt is required unless --list-models is used")

        # If the default model fails, try to find an alternative
        model_to_use = args.model
        if model_to_use == "imagen-3.0-generate-002":  # Only auto-detect if default is being used
            try:
                genai.GenerativeModel(model_to_use)  # Test if current default works
            except Exception:
                print(f"Warning: Model '{model_to_use}' not found. Attempting to find another image generation model...", file=sys.stderr)
                alternative_model = get_first_image_model()
                if alternative_model:
                    model_to_use = alternative_model
                    print(f"Using detected model: '{model_to_use}'", file=sys.stderr)
                else:
                    print("Error: No suitable image generation model found. Please run '--list-models' to see available models and specify one with '-m'.", file=sys.stderr)
                    return 1

        success = generate_and_save_image(args.prompt, args.output, model_to_use)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
