"""Command-line interface for Gemini image generation."""

import argparse
import os
import sys

from .core import configure_api, generate_and_save_image
from .utils import list_available_models


def main() -> int:
    """Run the CLI application."""
    parser = argparse.ArgumentParser(description="Generate images using Google Gemini API")
    parser.add_argument("prompt", nargs="?", help="Text prompt for image generation")
    parser.add_argument(
        "-o", "--output",
        default="generated_image.png",
        help="Output filename (default: generated_image.png)"
    )
    parser.add_argument(
        "-k", "--api-key",
        help="Gemini API key (can also use GEMINI_API_KEY environment variable)"
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List available models and exit"
    )
    
    args = parser.parse_args()

    try:
        configure_api(args.api_key)

        if args.list_models:
            list_available_models()
            return 0

        if not args.prompt:
            parser.error("prompt is required unless --list-models is used")

        success = generate_and_save_image(args.prompt, args.output)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
