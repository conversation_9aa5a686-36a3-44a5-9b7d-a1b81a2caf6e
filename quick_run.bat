@echo off
:: Quick run script for Gemini Image Generator
:: Minimal setup with auto-initialization

echo Initializing Gemini Image Generator...

:: Create venv if it doesn't exist
if not exist ".venv" (
    echo Creating virtual environment...
    uv venv
)

:: Install/sync dependencies
echo Syncing dependencies...
uv sync

:: Run with default or provided prompt
if "%~1"=="" (
    echo Running with default prompt...
    uv run gemini-img "A futuristic city at sunset with flying cars"
) else (
    echo Running with prompt: %*
    uv run gemini-img %*
)

echo.
echo Done! Check for generated_image.png (or your specified output file)
pause
