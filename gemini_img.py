import google.generativeai as genai
import os
from PIL import Image
import io

# ⚠️ IMPORTANT: Replace "YOUR_API_KEY" with your actual Gemini API key.
# It's recommended to set this as an environment variable for security.
# For example: os.environ["GEMINI_API_KEY"] = "YOUR_API_KEY"
genai.configure(api_key="YOUR_API_KEY")

def generate_and_save_image(prompt: str, output_filename: str = "generated_image.png"):
    """
    Generates an image using the Gemini API based on a prompt and saves it.

    Args:
        prompt (str): The text description for the image generation.
        output_filename (str): The name of the file to save the image as.
    """
    try:
        model = genai.GenerativeModel("gemini-1.5-flash") # Or another suitable Gemini model
        response = model.generate_content(
            ["Generate an image:", prompt],
            generation_config={"response_mime_type": "image/png"}
        )

        if response.images:
            image_bytes = response.images[0].pixels
            image = Image.open(io.BytesIO(image_bytes))
            image.save(output_filename)
            print(f"✅ Image successfully generated and saved as '{output_filename}'")
        else:
            print("❌ No image was returned by the API.")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    image_prompt = "A futuristic city at sunset with flying cars."
    generate_and_save_image(image_prompt)
