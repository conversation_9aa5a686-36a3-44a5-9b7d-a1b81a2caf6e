"""
Backward compatibility wrapper for the refactored gemini_img package.

⚠️ IMPORTANT: Set your Gemini API key as an environment variable:
   export GEMINI_API_KEY="your_api_key_here"

Or pass it directly to configure_api() function.
"""

from gemini_img import generate_and_save_image
from gemini_img.core import configure_api


def main():
    """Main function for CLI usage."""
    # Configure API - replace with your actual key or set GEMINI_API_KEY environment variable
    try:
        configure_api()  # Uses GEMINI_API_KEY environment variable
    except ValueError:
        configure_api("YOUR_API_KEY")  # Fallback - replace with actual key

    image_prompt = "A futuristic city at sunset with flying cars."
    generate_and_save_image(image_prompt)


if __name__ == "__main__":
    main()
