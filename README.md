# Gemini Image Generator

Simple image generation using Google Gemini API with modern Python tooling.

## Setup

1. Install dependencies using uv:
   ```bash
   uv sync
   ```

2. Set your Gemini API key:
   ```bash
   export GEMINI_API_KEY="your_api_key_here"
   ```

## Usage

### Command Line
```bash
# Using uv
uv run gemini-img "A futuristic city at sunset"

# With custom output filename
uv run gemini-img "A serene landscape" -o landscape.png
```

### Python API
```python
from gemini_img import generate_and_save_image
from gemini_img.core import configure_api

configure_api()  # Uses GEMINI_API_KEY environment variable
generate_and_save_image("A beautiful sunset", "sunset.png")
```

### Backward Compatibility
The original `gemini_img.py` script still works:
```bash
python gemini_img.py
```

## Project Structure

- `gemini_img/` - Main package
  - `core.py` - Core image generation functionality
  - `cli.py` - Command-line interface
- `gemini_img.py` - Backward compatibility wrapper
- `pyproject.toml` - Modern dependency management with uv
