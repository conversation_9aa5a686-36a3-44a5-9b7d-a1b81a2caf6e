# Gemini Image Generator

Simple image generation using Google Gemini API with modern Python tooling.

## Requirements

- Python 3.9 or higher
- uv package manager
- **Either**:
  - Google Gemini API key with access to image generation models, **OR**
  - OpenRouter API key (recommended alternative)

## Important Notes

This tool uses Google's image generation models:
- Uses `models/gemini-2.0-flash-preview-image-generation` by default (most likely to work)
- Automatically detects and uses other available image models if the default isn't available
- Allows specifying a custom model with the `-m` or `--model` flag
- **Billing Required**: Imagen models (imagen-3.0, imagen-4.0) require a paid Google Cloud project
- Requires a valid Google AI API key with image generation access
- Provides a `--list-models` option to see which models are available for your API key

## Setup

1. Install dependencies using uv:
   ```bash
   uv sync
   ```

2. Set your Gemini API key:
   ```bash
   export GEMINI_API_KEY="your_api_key_here"
   ```

## Usage

### Windows Batch Scripts (Recommended)

**Quick Run** - Simplest option:
```cmd
quick_run.bat "Your image prompt here"
```

**Standard Run** - With status messages:
```cmd
run.bat "A futuristic city at sunset"
```

**Advanced Run** - Full control with options:
```cmd
run_advanced.bat --help
run_advanced.bat "A serene landscape" -o landscape.png
run_advanced.bat -k YOUR_API_KEY "Custom prompt"
```

### Manual Command Line
```bash
# Using uv directly
uv run gemini-img "A futuristic city at sunset"

# With custom output filename
uv run gemini-img "A serene landscape" -o landscape.png

# With specific model
uv run gemini-img "A serene landscape" -m models/gemini-2.0-flash-preview-image-generation

# List available models
uv run gemini-img --list-models
```

### Python API
```python
from gemini_img import generate_and_save_image
from gemini_img.core import configure_api

configure_api()  # Uses GEMINI_API_KEY environment variable
generate_and_save_image("A beautiful sunset", "sunset.png")
```

### Backward Compatibility
The original `gemini_img.py` script still works:
```bash
python gemini_img.py
```

## Troubleshooting

### No Image Generation Models Available

If you see "No working image generation models found for your API key", try:

1. **First, test the most likely model**:
   ```bash
   uv run gemini-img "A simple red apple" -m models/gemini-2.0-flash-preview-image-generation
   ```

2. **Check available models**:
   ```bash
   uv run gemini-img --list-models
   ```
   Look for `models/gemini-2.0-flash-preview-image-generation` in the output.

3. **Enable billing for Imagen models**:
   - Visit [Google AI Studio](https://aistudio.google.com)
   - Click "Get API key" and follow billing setup if prompted
   - Imagen models (imagen-3.0, imagen-4.0) require a paid tier
   - Even with billing, you may get free tier usage initially

4. **Verify API access**:
   - Ensure your API key has access to the Generative Language API
   - Try creating an image generation project directly in Google AI Studio

5. **Regional considerations**:
   - Norway is a supported region for the Gemini API
   - Some specific features may have additional restrictions

## Project Structure

- `gemini_img/` - Main package
  - `core.py` - Core image generation functionality
  - `cli.py` - Command-line interface
  - `utils.py` - Model discovery and testing utilities
- `gemini_img.py` - Backward compatibility wrapper
- `pyproject.toml` - Modern dependency management with uv
